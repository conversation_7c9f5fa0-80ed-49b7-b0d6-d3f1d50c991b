hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/win32-x64@0.25.9':
    '@esbuild/win32-x64': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@rollup/rollup-win32-x64-msvc@4.48.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@sveltejs/acorn-typescript@1.0.5(acorn@8.15.0)':
    '@sveltejs/acorn-typescript': private
  '@sveltejs/vite-plugin-svelte-inspector@5.0.1(@sveltejs/vite-plugin-svelte@6.1.3(svelte@5.38.2)(vite@7.1.3))(svelte@5.38.2)(vite@7.1.3)':
    '@sveltejs/vite-plugin-svelte-inspector': private
  '@types/estree@1.0.8':
    '@types/estree': private
  acorn@8.15.0:
    acorn: private
  aria-query@5.3.2:
    aria-query: private
  axobject-query@4.1.0:
    axobject-query: private
  chokidar@4.0.3:
    chokidar: private
  clsx@2.1.1:
    clsx: private
  debug@4.4.1:
    debug: private
  deepmerge@4.3.1:
    deepmerge: private
  esbuild@0.25.9:
    esbuild: private
  esm-env@1.2.2:
    esm-env: private
  esrap@2.1.0:
    esrap: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  is-reference@3.0.3:
    is-reference: private
  kleur@4.1.5:
    kleur: private
  locate-character@3.0.0:
    locate-character: private
  magic-string@0.30.18:
    magic-string: private
  mri@1.2.0:
    mri: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  readdirp@4.1.2:
    readdirp: private
  rollup@4.48.0:
    rollup: private
  sade@1.8.1:
    sade: private
  source-map-js@1.2.1:
    source-map-js: private
  tinyglobby@0.2.14:
    tinyglobby: private
  vitefu@1.1.1(vite@7.1.3):
    vitefu: private
  zimmerframe@1.1.2:
    zimmerframe: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Sat, 23 Aug 2025 09:41:13 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-arm64@0.25.9'
  - '@esbuild/darwin-x64@0.25.9'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/linux-x64@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.25.9'
  - '@rollup/rollup-android-arm-eabi@4.48.0'
  - '@rollup/rollup-android-arm64@4.48.0'
  - '@rollup/rollup-darwin-arm64@4.48.0'
  - '@rollup/rollup-darwin-x64@4.48.0'
  - '@rollup/rollup-freebsd-arm64@4.48.0'
  - '@rollup/rollup-freebsd-x64@4.48.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.48.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.48.0'
  - '@rollup/rollup-linux-arm64-gnu@4.48.0'
  - '@rollup/rollup-linux-arm64-musl@4.48.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.48.0'
  - '@rollup/rollup-linux-ppc64-gnu@4.48.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.48.0'
  - '@rollup/rollup-linux-riscv64-musl@4.48.0'
  - '@rollup/rollup-linux-s390x-gnu@4.48.0'
  - '@rollup/rollup-linux-x64-gnu@4.48.0'
  - '@rollup/rollup-linux-x64-musl@4.48.0'
  - '@rollup/rollup-win32-arm64-msvc@4.48.0'
  - '@rollup/rollup-win32-ia32-msvc@4.48.0'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\桌面\AI编程\小游戏\ALU-Explorer\frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
